# Bigger Hitbox Script for FiveM

A FiveM resource that increases player hitbox sizes for easier targeting in combat scenarios.

## Features

- **Individual Player Control**: Each player controls their own hitbox independently
- **Adjustable Hitbox Size**: Customize hitbox multiplier from 0.1x to 5.0x
- **Toggle On/Off**: Enable or disable bigger hitboxes with commands
- **Admin Controls**: Server admins can control individual player hitboxes
- **Real-time Updates**: Changes apply immediately without restart
- **Debug Mode**: Optional debug information for troubleshooting

## Installation

1. Download or clone this repository
2. Place the folder in your FiveM server's `resources` directory
3. Add `ensure bigger-hitbox` to your `server.cfg`
4. Restart your server or use `refresh` and `start bigger-hitbox`

## Commands

### Client Commands
- `/togglehitbox` - Toggle your bigger hitbox on/off
- `/sethitbox [size]` - Set your hitbox size (0.1 to 5.0)
- `/hitboxdebug` - Toggle debug mode

### Admin Commands (Server)
- `/sethitboxplayer [playerid] [size]` - Set specific player's hitbox size
- `/resethitboxes` - Reset all hitboxes to normal size

## Configuration

Edit the `Config` table in `client.lua` to customize:

```lua
local Config = {
    hitboxSize = 2.0,           -- Default multiplier (2.0 = double size)
    enableForAllPlayers = false, -- Enable for all players or just self
    debugMode = false           -- Show debug information
}
```

Edit the server config in `server.lua`:

```lua
local Config = {
    allowClientControl = true,  -- Allow clients to control their own hitboxes
    maxHitboxSize = 5.0,       -- Maximum allowed hitbox size
    defaultHitboxSize = 2.0,   -- Default hitbox size
    logCommands = true         -- Log hitbox commands to console
}
```

## Permissions

For admin commands, make sure to set up proper ACE permissions in your `server.cfg`:

```
add_ace group.admin command.sethitboxplayer allow
add_ace group.admin command.resethitboxes allow
```

## How It Works

The script modifies individual player entity collision spheres and ped capsules to create larger hit detection areas. Each player controls their own hitbox independently. This makes it easier to hit targets in combat scenarios, which can be useful for:

- Training servers
- PvP events
- Roleplay scenarios requiring easier combat
- Accessibility improvements
- Individual player preferences

## Notes

- Each player controls their own hitbox independently
- Hitbox changes are visual/collision-based and don't affect actual player model size
- Changes are applied continuously to handle respawning
- Server-side validation prevents abuse of hitbox sizes
- Compatible with most other FiveM resources

## Troubleshooting

1. **Script not working**: Check console for errors and ensure resource is started
2. **Commands not responding**: Verify permissions are set correctly
3. **Hitboxes not applying**: Try toggling debug mode to see if changes are being applied
4. **Performance issues**: Increase the wait time in the main thread if needed

## Support

If you encounter issues or need help customizing the script, please check the console for error messages and ensure all files are properly installed.
