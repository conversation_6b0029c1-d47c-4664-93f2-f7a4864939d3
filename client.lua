-- Bigger Hitbox Script for FiveM
-- This script increases player hitbox sizes for easier targeting

local hitboxMultiplier = 2.0 -- Adjust this value to change hitbox size (1.0 = normal, 2.0 = double size)
local enabled = true

-- Configuration
local Config = {
    hitboxSize = 2.0,           -- Multiplier for hitbox size
    enableForAllPlayers = true,  -- Enable for all players or just self
    debugMode = false,          -- Show debug information
    keybind = 'F9'             -- Key to toggle the script
}

-- Function to set player hitbox size
function SetPlayerHitbox(playerId, multiplier)
    local playerPed = GetPlayerPed(playerId)
    if DoesEntityExist(playerPed) then
        -- Set the entity's collision sphere radius
        SetEntityCollisionSphere(playerPed, 0.0, 0.0, 0.0, multiplier)
        
        -- Modify the ped's capsule radius for better hit detection
        SetPedCapsule(playerPed, multiplier)
        
        if Config.debugMode then
            print(string.format("Set hitbox for player %d to %.1fx", playerId, multiplier))
        end
    end
end

-- Function to apply hitbox changes to all players
function ApplyHitboxToAllPlayers()
    if not enabled then return end
    
    local players = GetActivePlayers()
    for _, playerId in ipairs(players) do
        if Config.enableForAllPlayers or playerId == PlayerId() then
            SetPlayerHitbox(playerId, Config.hitboxSize)
        end
    end
end

-- Main thread
Citizen.CreateThread(function()
    while true do
        if enabled then
            ApplyHitboxToAllPlayers()
        end
        Citizen.Wait(1000) -- Check every second
    end
end)

-- Handle new players joining
AddEventHandler('playerSpawned', function()
    Citizen.Wait(1000) -- Wait a bit for the player to fully spawn
    if enabled then
        ApplyHitboxToAllPlayers()
    end
end)

-- Command to toggle the script
RegisterCommand('togglehitbox', function(source, args, rawCommand)
    enabled = not enabled
    local status = enabled and "enabled" or "disabled"
    
    if enabled then
        ApplyHitboxToAllPlayers()
        TriggerEvent('chat:addMessage', {
            color = {0, 255, 0},
            multiline = true,
            args = {"[Hitbox]", "Bigger hitboxes " .. status}
        })
    else
        -- Reset hitboxes to normal
        local players = GetActivePlayers()
        for _, playerId in ipairs(players) do
            SetPlayerHitbox(playerId, 1.0)
        end
        TriggerEvent('chat:addMessage', {
            color = {255, 0, 0},
            multiline = true,
            args = {"[Hitbox]", "Bigger hitboxes " .. status}
        })
    end
end, false)

-- Command to change hitbox size
RegisterCommand('sethitbox', function(source, args, rawCommand)
    if args[1] then
        local newSize = tonumber(args[1])
        if newSize and newSize > 0 and newSize <= 5.0 then
            Config.hitboxSize = newSize
            if enabled then
                ApplyHitboxToAllPlayers()
            end
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 255},
                multiline = true,
                args = {"[Hitbox]", string.format("Hitbox size set to %.1fx", newSize)}
            })
        else
            TriggerEvent('chat:addMessage', {
                color = {255, 255, 0},
                multiline = true,
                args = {"[Hitbox]", "Invalid size! Use a number between 0.1 and 5.0"}
            })
        end
    else
        TriggerEvent('chat:addMessage', {
            color = {255, 255, 0},
            multiline = true,
            args = {"[Hitbox]", "Usage: /sethitbox [size] (e.g., /sethitbox 2.5)"}
        })
    end
end, false)

-- Keybind to toggle (optional)
RegisterKeyMapping('togglehitbox', 'Toggle Bigger Hitboxes', 'keyboard', Config.keybind)

-- Debug command
RegisterCommand('hitboxdebug', function(source, args, rawCommand)
    Config.debugMode = not Config.debugMode
    local status = Config.debugMode and "enabled" or "disabled"
    TriggerEvent('chat:addMessage', {
        color = {255, 255, 255},
        multiline = true,
        args = {"[Hitbox Debug]", "Debug mode " .. status}
    })
end, false)

-- Handle server events for syncing
RegisterNetEvent('hitbox:updatePlayer')
AddEventHandler('hitbox:updatePlayer', function(playerId, hitboxSize)
    SetPlayerHitbox(playerId, hitboxSize)
end)

RegisterNetEvent('hitbox:resetAll')
AddEventHandler('hitbox:resetAll', function()
    local players = GetActivePlayers()
    for _, playerId in ipairs(players) do
        SetPlayerHitbox(playerId, 1.0)
    end
    enabled = false
    TriggerEvent('chat:addMessage', {
        color = {255, 165, 0},
        multiline = true,
        args = {"[Hitbox]", "All hitboxes reset by admin"}
    })
end)

-- Initialize
Citizen.CreateThread(function()
    Citizen.Wait(2000) -- Wait for everything to load
    if enabled then
        ApplyHitboxToAllPlayers()
        print("Bigger Hitbox Script loaded successfully!")
    end
end)
